// Expense & Goal Tracker Application
class ExpenseTracker {
  constructor() {
    this.expenses = JSON.parse(localStorage.getItem("expenses")) || [];
    this.goals = JSON.parse(localStorage.getItem("goals")) || [];
    this.settings = JSON.parse(localStorage.getItem("settings")) || {
      monthlyBudget: 50000,
      currency: "INR",
    };

    this.init();
  }

  init() {
    this.setupEventListeners();
    this.updateStats();
    this.renderExpenses();
    this.renderGoals();
    this.loadSettings();
    this.setupCharts();

    // Set today's date as default
    document.getElementById("expenseDate").value = new Date()
      .toISOString()
      .split("T")[0];
  }

  setupEventListeners() {
    // Tab navigation
    document.querySelectorAll(".tab-btn").forEach((btn) => {
      btn.addEventListener("click", (e) => {
        const tabName = e.target.dataset.tab;
        this.switchTab(tabName);
      });
    });

    // Expense form
    document.getElementById("addExpenseBtn").addEventListener("click", () => {
      document.getElementById("expenseForm").style.display = "block";
    });

    document.getElementById("cancelExpense").addEventListener("click", () => {
      this.hideExpenseForm();
    });

    document.getElementById("saveExpense").addEventListener("click", () => {
      this.saveExpense();
    });

    // Goal form
    document.getElementById("addGoalBtn").addEventListener("click", () => {
      document.getElementById("goalForm").style.display = "block";
    });

    document.getElementById("cancelGoal").addEventListener("click", () => {
      this.hideGoalForm();
    });

    document.getElementById("saveGoal").addEventListener("click", () => {
      this.saveGoal();
    });

    // Settings
    document.getElementById("saveSettings").addEventListener("click", () => {
      this.saveSettings();
    });

    // Data management
    document.getElementById("exportData").addEventListener("click", () => {
      this.exportData();
    });

    document.getElementById("clearData").addEventListener("click", () => {
      this.clearAllData();
    });
  }

  switchTab(tabName) {
    // Update tab buttons
    document.querySelectorAll(".tab-btn").forEach((btn) => {
      btn.classList.remove("active");
    });
    document.querySelector(`[data-tab="${tabName}"]`).classList.add("active");

    // Update tab content
    document.querySelectorAll(".tab-content").forEach((content) => {
      content.classList.remove("active");
    });
    document.getElementById(tabName).classList.add("active");

    // Update charts if analytics tab is selected
    if (tabName === "analytics") {
      setTimeout(() => this.updateCharts(), 100);
    }
  }

  saveExpense() {
    const amount = parseFloat(document.getElementById("expenseAmount").value);
    const category = document.getElementById("expenseCategory").value;
    const description = document.getElementById("expenseDescription").value;
    const date = document.getElementById("expenseDate").value;

    if (!amount || !category || !date) {
      alert("Please fill in all required fields");
      return;
    }

    const expense = {
      id: Date.now(),
      amount,
      category,
      description,
      date,
      timestamp: new Date().toISOString(),
    };

    this.expenses.unshift(expense);
    this.saveToStorage();
    this.updateStats();
    this.renderExpenses();
    this.hideExpenseForm();
    this.clearExpenseForm();
  }

  saveGoal() {
    const title = document.getElementById("goalTitle").value;
    const amount = parseFloat(document.getElementById("goalAmount").value);
    const deadline = document.getElementById("goalDeadline").value;
    const description = document.getElementById("goalDescription").value;

    if (!title || !amount || !deadline) {
      alert("Please fill in all required fields");
      return;
    }

    const goal = {
      id: Date.now(),
      title,
      amount,
      deadline,
      description,
      currentAmount: 0,
      timestamp: new Date().toISOString(),
    };

    this.goals.unshift(goal);
    this.saveToStorage();
    this.renderGoals();
    this.hideGoalForm();
    this.clearGoalForm();
  }

  deleteExpense(id) {
    if (confirm("Are you sure you want to delete this expense?")) {
      this.expenses = this.expenses.filter((expense) => expense.id !== id);
      this.saveToStorage();
      this.updateStats();
      this.renderExpenses();
    }
  }

  deleteGoal(id) {
    if (confirm("Are you sure you want to delete this goal?")) {
      this.goals = this.goals.filter((goal) => goal.id !== id);
      this.saveToStorage();
      this.renderGoals();
    }
  }

  updateGoalProgress(id, amount) {
    const goal = this.goals.find((g) => g.id === id);
    if (goal) {
      goal.currentAmount = Math.min(amount, goal.amount);
      this.saveToStorage();
      this.renderGoals();
    }
  }

  hideExpenseForm() {
    document.getElementById("expenseForm").style.display = "none";
  }

  hideGoalForm() {
    document.getElementById("goalForm").style.display = "none";
  }

  clearExpenseForm() {
    document.getElementById("expenseAmount").value = "";
    document.getElementById("expenseCategory").value = "";
    document.getElementById("expenseDescription").value = "";
    document.getElementById("expenseDate").value = new Date()
      .toISOString()
      .split("T")[0];
  }

  clearGoalForm() {
    document.getElementById("goalTitle").value = "";
    document.getElementById("goalAmount").value = "";
    document.getElementById("goalDeadline").value = "";
    document.getElementById("goalDescription").value = "";
  }

  updateStats() {
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();

    const monthlyExpenses = this.expenses.filter((expense) => {
      const expenseDate = new Date(expense.date);
      return (
        expenseDate.getMonth() === currentMonth &&
        expenseDate.getFullYear() === currentYear
      );
    });

    const totalExpenses = monthlyExpenses.reduce(
      (sum, expense) => sum + expense.amount,
      0
    );
    const remainingBudget = this.settings.monthlyBudget - totalExpenses;

    document.getElementById("totalExpenses").textContent =
      this.formatCurrency(totalExpenses);
    document.getElementById("monthlyBudget").textContent = this.formatCurrency(
      this.settings.monthlyBudget
    );
    document.getElementById("remainingBudget").textContent =
      this.formatCurrency(remainingBudget);

    // Update remaining budget color
    const remainingElement = document.getElementById("remainingBudget");
    if (remainingBudget < 0) {
      remainingElement.style.color = "#ff6b6b";
    } else if (remainingBudget < this.settings.monthlyBudget * 0.2) {
      remainingElement.style.color = "#ffa726";
    } else {
      remainingElement.style.color = "#66bb6a";
    }
  }

  formatCurrency(amount) {
    const symbol =
      this.settings.currency === "INR"
        ? "₹"
        : this.settings.currency === "USD"
        ? "$"
        : "€";
    return `${symbol}${amount.toLocaleString()}`;
  }

  renderExpenses() {
    const container = document.getElementById("expensesList");

    if (this.expenses.length === 0) {
      container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-receipt"></i>
                    <p>No expenses recorded yet. Add your first expense to get started!</p>
                </div>
            `;
      return;
    }

    container.innerHTML = this.expenses
      .map(
        (expense) => `
            <div class="expense-item">
                <div class="expense-header">
                    <div class="expense-amount">${this.formatCurrency(
                      expense.amount
                    )}</div>
                    <div class="expense-category">${this.getCategoryIcon(
                      expense.category
                    )} ${this.getCategoryName(expense.category)}</div>
                </div>
                <div class="expense-description">${
                  expense.description || "No description"
                }</div>
                <div class="expense-date">${this.formatDate(expense.date)}</div>
                <button class="delete-btn" onclick="tracker.deleteExpense(${
                  expense.id
                })">
                    <i class="fas fa-trash"></i> Delete
                </button>
            </div>
        `
      )
      .join("");
  }

  renderGoals() {
    const container = document.getElementById("goalsList");

    if (this.goals.length === 0) {
      container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-target"></i>
                    <p>No goals set yet. Create your first financial goal!</p>
                </div>
            `;
      return;
    }

    container.innerHTML = this.goals
      .map((goal) => {
        const progress = (goal.currentAmount / goal.amount) * 100;
        const daysLeft = Math.ceil(
          (new Date(goal.deadline) - new Date()) / (1000 * 60 * 60 * 24)
        );

        return `
                <div class="goal-item">
                    <div class="goal-header">
                        <h4>${goal.title}</h4>
                        <div class="goal-amount">${this.formatCurrency(
                          goal.amount
                        )}</div>
                    </div>
                    <div class="goal-description">${
                      goal.description || "No description"
                    }</div>
                    <div class="goal-deadline">Target: ${this.formatDate(
                      goal.deadline
                    )} (${daysLeft} days left)</div>
                    <div class="goal-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${progress}%"></div>
                        </div>
                        <div class="progress-text">
                            <span>${this.formatCurrency(
                              goal.currentAmount
                            )} saved</span>
                            <span>${progress.toFixed(1)}%</span>
                        </div>
                        <div style="margin-top: 10px;">
                            <input type="number" placeholder="Add to goal" id="goalInput${
                              goal.id
                            }" style="width: 150px; margin-right: 10px;">
                            <button class="btn-primary" onclick="tracker.addToGoal(${
                              goal.id
                            })">Add Amount</button>
                            <button class="delete-btn" onclick="tracker.deleteGoal(${
                              goal.id
                            })" style="margin-left: 10px;">Delete</button>
                        </div>
                    </div>
                </div>
            `;
      })
      .join("");
  }

  addToGoal(id) {
    const input = document.getElementById(`goalInput${id}`);
    const amount = parseFloat(input.value);

    if (!amount || amount <= 0) {
      alert("Please enter a valid amount");
      return;
    }

    const goal = this.goals.find((g) => g.id === id);
    if (goal) {
      goal.currentAmount = Math.min(goal.currentAmount + amount, goal.amount);
      this.saveToStorage();
      this.renderGoals();
      input.value = "";
    }
  }

  getCategoryIcon(category) {
    const icons = {
      food: "🍽️",
      transport: "🚗",
      shopping: "🛍️",
      bills: "💡",
      entertainment: "🎬",
      health: "🏥",
      education: "📚",
      other: "📦",
    };
    return icons[category] || "📦";
  }

  getCategoryName(category) {
    const names = {
      food: "Food & Dining",
      transport: "Transportation",
      shopping: "Shopping",
      bills: "Bills & Utilities",
      entertainment: "Entertainment",
      health: "Healthcare",
      education: "Education",
      other: "Other",
    };
    return names[category] || "Other";
  }

  formatDate(dateString) {
    return new Date(dateString).toLocaleDateString("en-IN", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  }

  saveToStorage() {
    localStorage.setItem("expenses", JSON.stringify(this.expenses));
    localStorage.setItem("goals", JSON.stringify(this.goals));
    localStorage.setItem("settings", JSON.stringify(this.settings));
  }

  loadSettings() {
    document.getElementById("monthlyBudgetInput").value =
      this.settings.monthlyBudget;
    document.getElementById("currencySelect").value = this.settings.currency;
  }

  saveSettings() {
    const monthlyBudget = parseFloat(
      document.getElementById("monthlyBudgetInput").value
    );
    const currency = document.getElementById("currencySelect").value;

    if (!monthlyBudget || monthlyBudget <= 0) {
      alert("Please enter a valid monthly budget");
      return;
    }

    this.settings.monthlyBudget = monthlyBudget;
    this.settings.currency = currency;
    this.saveToStorage();
    this.updateStats();
    alert("Settings saved successfully!");
  }

  setupCharts() {
    this.categoryChart = null;
    this.trendChart = null;
  }

  updateCharts() {
    this.updateCategoryChart();
    this.updateTrendChart();
  }

  updateCategoryChart() {
    const ctx = document.getElementById("categoryChart").getContext("2d");

    // Destroy existing chart
    if (this.categoryChart) {
      this.categoryChart.destroy();
    }

    // Calculate category totals
    const categoryTotals = {};
    this.expenses.forEach((expense) => {
      categoryTotals[expense.category] =
        (categoryTotals[expense.category] || 0) + expense.amount;
    });

    const labels = Object.keys(categoryTotals).map((cat) =>
      this.getCategoryName(cat)
    );
    const data = Object.values(categoryTotals);
    const colors = [
      "#667eea",
      "#764ba2",
      "#f093fb",
      "#f5576c",
      "#4facfe",
      "#00f2fe",
      "#43e97b",
      "#38f9d7",
    ];

    this.categoryChart = new Chart(ctx, {
      type: "doughnut",
      data: {
        labels: labels,
        datasets: [
          {
            data: data,
            backgroundColor: colors.slice(0, labels.length),
            borderWidth: 0,
          },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: "bottom",
          },
        },
      },
    });
  }

  updateTrendChart() {
    const ctx = document.getElementById("trendChart").getContext("2d");

    // Destroy existing chart
    if (this.trendChart) {
      this.trendChart.destroy();
    }

    // Get last 6 months data
    const monthlyData = {};
    const currentDate = new Date();

    for (let i = 5; i >= 0; i--) {
      const date = new Date(
        currentDate.getFullYear(),
        currentDate.getMonth() - i,
        1
      );
      const monthKey = `${date.getFullYear()}-${String(
        date.getMonth() + 1
      ).padStart(2, "0")}`;
      const monthName = date.toLocaleDateString("en-IN", {
        month: "short",
        year: "numeric",
      });
      monthlyData[monthKey] = { name: monthName, total: 0 };
    }

    // Calculate monthly totals
    this.expenses.forEach((expense) => {
      const expenseDate = new Date(expense.date);
      const monthKey = `${expenseDate.getFullYear()}-${String(
        expenseDate.getMonth() + 1
      ).padStart(2, "0")}`;
      if (monthlyData[monthKey]) {
        monthlyData[monthKey].total += expense.amount;
      }
    });

    const labels = Object.values(monthlyData).map((month) => month.name);
    const data = Object.values(monthlyData).map((month) => month.total);

    this.trendChart = new Chart(ctx, {
      type: "line",
      data: {
        labels: labels,
        datasets: [
          {
            label: "Monthly Expenses",
            data: data,
            borderColor: "#667eea",
            backgroundColor: "rgba(102, 126, 234, 0.1)",
            borderWidth: 3,
            fill: true,
            tension: 0.4,
          },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false,
          },
        },
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              callback: function (value) {
                return "₹" + value.toLocaleString();
              },
            },
          },
        },
      },
    });
  }

  exportData() {
    const data = {
      expenses: this.expenses,
      goals: this.goals,
      settings: this.settings,
      exportDate: new Date().toISOString(),
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], {
      type: "application/json",
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `expense-tracker-backup-${
      new Date().toISOString().split("T")[0]
    }.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  clearAllData() {
    if (
      confirm(
        "Are you sure you want to clear all data? This action cannot be undone."
      )
    ) {
      if (
        confirm(
          "This will delete all your expenses, goals, and settings. Are you absolutely sure?"
        )
      ) {
        localStorage.clear();
        this.expenses = [];
        this.goals = [];
        this.settings = { monthlyBudget: 50000, currency: "INR" };

        this.updateStats();
        this.renderExpenses();
        this.renderGoals();
        this.loadSettings();

        alert("All data has been cleared successfully.");
      }
    }
  }
}

// Initialize the application
let tracker;
document.addEventListener("DOMContentLoaded", () => {
  tracker = new ExpenseTracker();
});
