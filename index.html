<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Expense & Goal Tracker</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fas fa-chart-line"></i> Expense & Goal Tracker</h1>
            <div class="header-stats">
                <div class="stat-card">
                    <div class="stat-value" id="totalExpenses">₹0</div>
                    <div class="stat-label">Total Expenses</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="monthlyBudget">₹0</div>
                    <div class="stat-label">Monthly Budget</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="remainingBudget">₹0</div>
                    <div class="stat-label">Remaining</div>
                </div>
            </div>
        </header>

        <nav class="tab-nav">
            <button class="tab-btn active" data-tab="expenses">
                <i class="fas fa-receipt"></i> Expenses
            </button>
            <button class="tab-btn" data-tab="goals">
                <i class="fas fa-target"></i> Goals
            </button>
            <button class="tab-btn" data-tab="analytics">
                <i class="fas fa-chart-pie"></i> Analytics
            </button>
            <button class="tab-btn" data-tab="settings">
                <i class="fas fa-cog"></i> Settings
            </button>
        </nav>

        <!-- Expenses Tab -->
        <div class="tab-content active" id="expenses">
            <div class="section-header">
                <h2>Add New Expense</h2>
                <button class="btn-primary" id="addExpenseBtn">
                    <i class="fas fa-plus"></i> Add Expense
                </button>
            </div>

            <div class="expense-form" id="expenseForm" style="display: none;">
                <div class="form-group">
                    <label for="expenseAmount">Amount (₹)</label>
                    <input type="number" id="expenseAmount" placeholder="Enter amount" required>
                </div>
                <div class="form-group">
                    <label for="expenseCategory">Category</label>
                    <select id="expenseCategory" required>
                        <option value="">Select category</option>
                        <option value="food">🍽️ Food & Dining</option>
                        <option value="transport">🚗 Transportation</option>
                        <option value="shopping">🛍️ Shopping</option>
                        <option value="bills">💡 Bills & Utilities</option>
                        <option value="entertainment">🎬 Entertainment</option>
                        <option value="health">🏥 Healthcare</option>
                        <option value="education">📚 Education</option>
                        <option value="other">📦 Other</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="expenseDescription">Description</label>
                    <input type="text" id="expenseDescription" placeholder="What did you spend on?">
                </div>
                <div class="form-group">
                    <label for="expenseDate">Date</label>
                    <input type="date" id="expenseDate" required>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn-secondary" id="cancelExpense">Cancel</button>
                    <button type="button" class="btn-primary" id="saveExpense">Save Expense</button>
                </div>
            </div>

            <div class="expenses-list">
                <h3>Recent Expenses</h3>
                <div id="expensesList"></div>
            </div>
        </div>

        <!-- Goals Tab -->
        <div class="tab-content" id="goals">
            <div class="section-header">
                <h2>Financial Goals</h2>
                <button class="btn-primary" id="addGoalBtn">
                    <i class="fas fa-plus"></i> Add Goal
                </button>
            </div>

            <div class="goal-form" id="goalForm" style="display: none;">
                <div class="form-group">
                    <label for="goalTitle">Goal Title</label>
                    <input type="text" id="goalTitle" placeholder="e.g., Emergency Fund" required>
                </div>
                <div class="form-group">
                    <label for="goalAmount">Target Amount (₹)</label>
                    <input type="number" id="goalAmount" placeholder="Enter target amount" required>
                </div>
                <div class="form-group">
                    <label for="goalDeadline">Target Date</label>
                    <input type="date" id="goalDeadline" required>
                </div>
                <div class="form-group">
                    <label for="goalDescription">Description</label>
                    <textarea id="goalDescription" placeholder="Describe your goal..."></textarea>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn-secondary" id="cancelGoal">Cancel</button>
                    <button type="button" class="btn-primary" id="saveGoal">Save Goal</button>
                </div>
            </div>

            <div class="goals-list">
                <div id="goalsList"></div>
            </div>
        </div>

        <!-- Analytics Tab -->
        <div class="tab-content" id="analytics">
            <h2>Spending Analytics</h2>
            <div class="analytics-grid">
                <div class="chart-container">
                    <h3>Expenses by Category</h3>
                    <canvas id="categoryChart"></canvas>
                </div>
                <div class="chart-container">
                    <h3>Monthly Spending Trend</h3>
                    <canvas id="trendChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Settings Tab -->
        <div class="tab-content" id="settings">
            <h2>Settings</h2>
            <div class="settings-form">
                <div class="form-group">
                    <label for="monthlyBudgetInput">Monthly Budget (₹)</label>
                    <input type="number" id="monthlyBudgetInput" placeholder="Enter your monthly budget">
                </div>
                <div class="form-group">
                    <label for="currencySelect">Currency</label>
                    <select id="currencySelect">
                        <option value="INR">₹ Indian Rupee</option>
                        <option value="USD">$ US Dollar</option>
                        <option value="EUR">€ Euro</option>
                    </select>
                </div>
                <button class="btn-primary" id="saveSettings">Save Settings</button>
            </div>

            <div class="data-management">
                <h3>Data Management</h3>
                <button class="btn-secondary" id="exportData">
                    <i class="fas fa-download"></i> Export Data
                </button>
                <button class="btn-danger" id="clearData">
                    <i class="fas fa-trash"></i> Clear All Data
                </button>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="script.js"></script>
</body>
</html>
