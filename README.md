# 💰 Expense & Goal Tracker

A modern, responsive web application for tracking your daily expenses and financial goals. Built with vanilla HTML, CSS, and JavaScript with beautiful charts and analytics.

## ✨ Features

### 📊 Expense Tracking
- **Add Expenses**: Record expenses with amount, category, description, and date
- **Categories**: Pre-defined categories with emojis (Food, Transport, Shopping, Bills, etc.)
- **Real-time Stats**: View total expenses, monthly budget, and remaining budget
- **Visual Feedback**: Color-coded budget status (green/yellow/red)

### 🎯 Goal Management
- **Set Financial Goals**: Create goals with target amounts and deadlines
- **Track Progress**: Visual progress bars showing completion percentage
- **Add Contributions**: Easily add money to your goals
- **Deadline Tracking**: See days remaining for each goal

### 📈 Analytics & Charts
- **Category Breakdown**: Doughnut chart showing spending by category
- **Monthly Trends**: Line chart displaying spending patterns over 6 months
- **Interactive Charts**: Powered by Chart.js for smooth animations

### ⚙️ Settings & Data Management
- **Monthly Budget**: Set and adjust your monthly spending limit
- **Currency Support**: Choose between INR (₹), USD ($), or EUR (€)
- **Data Export**: Download your data as JSON backup
- **Data Management**: Clear all data with confirmation prompts

## 🚀 Getting Started

1. **Open the Application**
   - Simply open `index.html` in your web browser
   - No installation or server setup required!

2. **Set Your Budget**
   - Go to Settings tab
   - Enter your monthly budget
   - Choose your preferred currency

3. **Start Tracking**
   - Add your first expense in the Expenses tab
   - Create financial goals in the Goals tab
   - View analytics in the Analytics tab

## 📱 Mobile Responsive

The application is fully responsive and works great on:
- 💻 Desktop computers
- 📱 Mobile phones
- 📟 Tablets

## 💾 Data Storage

- All data is stored locally in your browser using localStorage
- No data is sent to external servers
- Your privacy is completely protected
- Data persists between browser sessions

## 🎨 Design Features

- **Modern UI**: Clean, gradient-based design with glassmorphism effects
- **Smooth Animations**: Hover effects and transitions throughout
- **Icon Integration**: Font Awesome icons for better visual appeal
- **Color Coding**: Intuitive color schemes for different data types

## 📊 Expense Categories

The app includes 8 pre-defined categories:
- 🍽️ Food & Dining
- 🚗 Transportation
- 🛍️ Shopping
- 💡 Bills & Utilities
- 🎬 Entertainment
- 🏥 Healthcare
- 📚 Education
- 📦 Other

## 🔧 Technical Details

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Charts**: Chart.js library
- **Icons**: Font Awesome
- **Storage**: Browser localStorage
- **Responsive**: CSS Grid and Flexbox

## 📈 Analytics Features

### Category Chart
- Visual breakdown of spending by category
- Doughnut chart with custom colors
- Responsive design for all screen sizes

### Trend Chart
- 6-month spending trend analysis
- Line chart with smooth curves
- Formatted currency values on Y-axis

## 🎯 Goal Tracking Features

- **Progress Visualization**: Animated progress bars
- **Deadline Tracking**: Days remaining calculation
- **Flexible Contributions**: Add any amount to goals
- **Goal Management**: Easy creation and deletion

## 💡 Tips for Best Use

1. **Daily Tracking**: Add expenses daily for accurate tracking
2. **Categorize Properly**: Use appropriate categories for better analytics
3. **Set Realistic Goals**: Create achievable financial targets
4. **Regular Review**: Check analytics monthly to understand spending patterns
5. **Budget Monitoring**: Keep an eye on remaining budget throughout the month

## 🔒 Privacy & Security

- **Local Storage Only**: No data leaves your device
- **No Registration**: No accounts or personal information required
- **Offline Capable**: Works without internet connection
- **Data Control**: You own and control all your data

## 🚀 Future Enhancements

Potential features for future versions:
- Import/Export CSV functionality
- Recurring expense templates
- Budget alerts and notifications
- Multiple currency support
- Advanced filtering and search
- Expense receipt photo attachments

## 🤝 Contributing

This is an open-source project. Feel free to:
- Report bugs or issues
- Suggest new features
- Contribute code improvements
- Share feedback and ideas

## 📄 License

This project is open source and available under the MIT License.

---

**Happy Tracking! 💰📊**

Start managing your finances better today with this comprehensive expense and goal tracking solution!
